import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { KeywordSearchImpressionsDto } from '@malou-io/package-dto';
import { newDbId, toDbId } from '@malou-io/package-models';
import { KeywordSearchImpressionsType, MAX_GMB_API_FETCH_MONTHS, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { SearchKeywordsImpressionsService } from ':microservices/search-keywords-impressions.service';
import { getDefaultGmbCredential } from ':modules/credentials/tests/credential.builder';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import { getManyKeywordSearchImpressions } from ':modules/keyword-search-impressions/tests/keyword-search-impressions.builder';
import { FetchAndSaveKeywordsSearchImpressionsUseCase } from ':modules/keyword-search-impressions/use-cases/fetch-and-save-keyword-search-impressions/fetch-and-save-keywords-search-impressions.use-case';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GmbTokenProvider } from ':providers/google/gmb-api-token';
import { GmbBusinessProfilePerformanceProvider } from ':providers/google/gmb.business-profile-performance.provider';

describe('FetchAndSaveKeywordsSearchImpressionsUseCase', () => {
    const gmbApiResponse = [
        { searchKeyword: 'restaurants', insightsValue: { value: '2772' } },
        { searchKeyword: 'restaurant', insightsValue: { value: '220' } },
        { searchKeyword: 'restaurantes', insightsValue: { value: '168' } },
        { searchKeyword: 'brazilian restaurant', insightsValue: { value: '145' } },
        { searchKeyword: 'restaurante brasileiro', insightsValue: { value: '121' } },
    ];

    const previousMonth = DateTime.now().minus({ months: 1 });
    const lambdaResponse = {
        data: {
            month: previousMonth.month,
            year: previousMonth.year,
            monthImpressionsData: [
                {
                    keywordSearch: 'restaurants',
                    value: 2772,
                    type: KeywordSearchImpressionsType.DISCOVERY,
                    relatedSelectedKeywords: ['66583dead6d813e3d041eed7', '6661c3b05ac1ccadcf5ed25b'],
                },
                {
                    keywordSearch: 'restaurant',
                    value: 220,
                    type: KeywordSearchImpressionsType.DISCOVERY,
                    relatedSelectedKeywords: ['66583dead6d813e3d041eed7'],
                },
                {
                    keywordSearch: 'restaurantes',
                    value: 168,
                    type: KeywordSearchImpressionsType.DISCOVERY,
                    relatedSelectedKeywords: ['6661c3b05ac1ccadcf5ed25b'],
                },
                {
                    keywordSearch: 'brazilian restaurant',
                    value: 145,
                    type: KeywordSearchImpressionsType.DISCOVERY,
                    relatedSelectedKeywords: ['66583dead6d813e3d041eed7', '6661c3b05ac1ccadcf5ed25b'],
                },
                {
                    keywordSearch: 'restaurante brasileiro',
                    value: 121,
                    type: KeywordSearchImpressionsType.BRANDING,
                    relatedSelectedKeywords: ['66583dead6d813e3d041eed7'],
                },
            ],
            selectedKeywordsImpressions: [
                {
                    id: '66583dead6d813e3d041eed7',
                    text: 'ambiance restaurant italien',
                    value: 3424,
                },
                {
                    id: '66588a323a935f69769e6303',
                    text: 'bar ice cream',
                    value: 0,
                },
                {
                    id: '6661c3b05ac1ccadcf5ed25b',
                    text: 'restaurant',
                    value: 3487,
                },
                {
                    id: '669a349b675625fbcc94dbcf',
                    text: 'restaurant in paris 11',
                    value: 108,
                },
                {
                    id: '66f1826a7ab36e9d47a763c4',
                    text: 'meat',
                    value: 160,
                },
                {
                    id: '67740076f955a8f9c40f12b0',
                    text: 'spanish drink quiche',
                    value: 0,
                },
                {
                    id: '6774008cf955a8f9c40f12ec',
                    text: 'ravioli pates',
                    value: 0,
                },
                {
                    id: '67740379f955a8f9c40f1e58',
                    text: 'bread and butter restaurant',
                    value: 0,
                },
                {
                    id: '677cfb000e7efbbbb2b6fc1a',
                    text: 'bar paris 11',
                    value: 144,
                },
                {
                    id: '677d376673c21dddb3325c1a',
                    text: 'restaurant paris',
                    value: 163,
                },
            ],
        },
    };

    class GmbBusinessProfilePerformanceProviderMock {
        async fetchSearchKeywordsImpressions(_params: any) {
            return gmbApiResponse;
        }
    }

    class SearchKeywordsImpressionsServiceMock {
        async processKeywordSearchImpressions(_params: any) {
            return Promise.resolve(lambdaResponse);
        }
    }

    class GmbTokenProviderMock {
        public getRefreshToken() {
            return {
                access_token: 'access_token',
                expires_in: 1000,
                scope: 'scope',
                token_type: 'token_type',
            };
        }
    }

    let fetchAndSaveKeywordsSearchImpressionsUseCase: FetchAndSaveKeywordsSearchImpressionsUseCase;

    beforeAll(() => {
        registerRepositories([
            'KeywordSearchImpressionsRepository',
            'RestaurantKeywordsRepository',
            'PlatformsRepository',
            'RestaurantKeywordsRepository',
            'RestaurantsRepository',
            'OrganizationsRepository',
            'GmbCredentialsRepository',
            'KeywordsTempRepository',
        ]);

        container.register(GmbBusinessProfilePerformanceProvider, { useValue: new GmbBusinessProfilePerformanceProviderMock() as any });
        container.register(SearchKeywordsImpressionsService, { useValue: new SearchKeywordsImpressionsServiceMock() as any });
        container.register(GmbTokenProvider, {
            useValue: new GmbTokenProviderMock() as any,
        });

        fetchAndSaveKeywordsSearchImpressionsUseCase = container.resolve(FetchAndSaveKeywordsSearchImpressionsUseCase);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('execute', () => {
        it('should throw error if platform does not exist', async () => {
            const platformId = newDbId();

            const promise = fetchAndSaveKeywordsSearchImpressionsUseCase.execute({ platformId: platformId.toString() });

            await expect(promise).rejects.toThrow('Platform not found');
        });

        it('should fetch and upsert keyword search impressions and save impressionsHistory in restaurantKeywords', async () => {
            const keywordSearchImpressionsRepository = container.resolve(KeywordSearchImpressionsRepository);
            const restaurantKeywordsRepository = container.resolve(RestaurantKeywordsRepository);

            const testCase = new TestCaseBuilderV2<
                | 'restaurants'
                | 'platforms'
                | 'gmbCredentials'
                | 'keywordSearchImpressions'
                | 'restaurantKeywords'
                | 'organizations'
                | 'keywordsTemp'
            >({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    gmbCredentials: {
                        data() {
                            return [getDefaultGmbCredential().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(PlatformKey.GMB)
                                    .credentials([dependencies.gmbCredentials()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                    keywordSearchImpressions: {
                        data(dependencies) {
                            // this is default case. only previous month data are missing
                            // timeRange in use case is [previousMonth]
                            return getManyKeywordSearchImpressions(
                                dependencies.restaurants()[0]._id,
                                DateTime.now().minus({ months: MAX_GMB_API_FETCH_MONTHS }).toJSDate(),
                                DateTime.now().minus({ months: 2 }).toJSDate()
                            );
                        },
                    },
                    keywordsTemp: {
                        data() {
                            return lambdaResponse.data.selectedKeywordsImpressions.map((keyword, i) =>
                                getDefaultKeywordTemp()._id(toDbId(keyword.id)).text(`text${i}`).build()
                            );
                        },
                    },
                    restaurantKeywords: {
                        data(dependencies) {
                            const restaurantId = dependencies.restaurants()[0]._id;
                            return dependencies
                                .keywordsTemp()
                                .map((keyword) =>
                                    getDefaultRestaurantKeyword().restaurantId(restaurantId).keywordId(keyword._id).selected(true).build()
                                );
                        },
                    },
                },
                expectedResult(dependencies): {
                    keywordSearchImpressions: KeywordSearchImpressionsDto[];
                    partialRestaurantKeywords: { keywordId: string; impressionsHistory: { date: Date; value: number }[] }[];
                } {
                    const restaurantId = dependencies.restaurants[0]._id.toString();
                    const keywordSearchImpressions = lambdaResponse.data.monthImpressionsData.map((keywordSearch) => ({
                        id: expect.any(String),
                        restaurantId,
                        year: previousMonth.year,
                        month: previousMonth.month,
                        keywordSearch: keywordSearch.keywordSearch,
                        value: keywordSearch.value,
                        type: keywordSearch.type,
                        relatedKeywordIds: keywordSearch.relatedSelectedKeywords,
                    }));
                    const partialRestaurantKeywords = lambdaResponse.data.selectedKeywordsImpressions.map((keyword) => ({
                        keywordId: keyword.id,
                        impressionsHistory: [{ date: expect.any(Date), value: keyword.value }],
                    }));

                    return {
                        keywordSearchImpressions,
                        partialRestaurantKeywords,
                    };
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedKeywordSearchImpressions = testCase.getExpectedResult().keywordSearchImpressions;
            const expectedPartialRestaurantKeywords = testCase.getExpectedResult().partialRestaurantKeywords;

            const platformId = seededObjects.platforms[0]._id.toString();
            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const previousMonthYearMonthIndex = previousMonth.year * 100 + previousMonth.month;

            // before use case execution
            // -------------------------------------------------------------------------------------

            // assert that keywordSearchImpressions for previous month do not exist
            const currentKeywordSearchImpressions = await keywordSearchImpressionsRepository.find({
                filter: { restaurantId, yearMonthIndex: previousMonthYearMonthIndex },
                options: { lean: true },
            });
            expect(currentKeywordSearchImpressions).toEqual([]);

            // assert that restaurantKeywords have no impressionsHistory
            const restaurantKeywords = await restaurantKeywordsRepository.find({
                filter: { restaurantId, selected: true },
                options: { lean: true },
            });
            restaurantKeywords.forEach((restaurantKeyword) => {
                expect(restaurantKeyword.impressionsHistory).toEqual([]);
            });

            // after use case execution
            // -------------------------------------------------------------------------------------
            await fetchAndSaveKeywordsSearchImpressionsUseCase.execute({ platformId });

            // assert that keywordSearchImpressions for previous month have been created
            const keywordSearchImpressions = await keywordSearchImpressionsRepository.find({
                filter: { restaurantId, yearMonthIndex: previousMonthYearMonthIndex },
                options: { lean: true },
            });
            const keywordSearchImpressionsDTO = keywordSearchImpressions.map((doc) =>
                keywordSearchImpressionsRepository.toEntity(doc).toDTO()
            );
            expect(expectedKeywordSearchImpressions).toIncludeSameMembers(keywordSearchImpressionsDTO);

            // assert that restaurantKeywords have impressionsHistory
            const updatedRestaurantKeywords = await restaurantKeywordsRepository.find({
                filter: { restaurantId, selected: true },
                options: { lean: true },
            });
            updatedRestaurantKeywords.forEach((restaurantKeyword) => {
                const expectedImpressionsHistory = expectedPartialRestaurantKeywords.find(
                    (partialRestaurantKeyword) => partialRestaurantKeyword.keywordId === restaurantKeyword.keywordId.toString()
                ).impressionsHistory;
                expect(restaurantKeyword.impressionsHistory).toHaveLength(expectedImpressionsHistory.length);
                expect(restaurantKeyword.impressionsHistory).toIncludeSameMembers(expectedImpressionsHistory);
            });
        });

        it('should only fetch and upsert keyword search impressions if no selected restaurantKeywords', async () => {
            const keywordSearchImpressionsRepository = container.resolve(KeywordSearchImpressionsRepository);
            const restaurantKeywordsRepository = container.resolve(RestaurantKeywordsRepository);

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'gmbCredentials' | 'keywordSearchImpressions' | 'organizations'
            >({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    gmbCredentials: {
                        data() {
                            return [getDefaultGmbCredential().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(PlatformKey.GMB)
                                    .credentials([dependencies.gmbCredentials()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                    keywordSearchImpressions: {
                        data(dependencies) {
                            // this is default case. only previous month data are missing
                            // timeRange in use case is [previousMonth]
                            return getManyKeywordSearchImpressions(
                                dependencies.restaurants()[0]._id,
                                DateTime.now().minus({ months: MAX_GMB_API_FETCH_MONTHS }).toJSDate(),
                                DateTime.now().minus({ months: 2 }).toJSDate()
                            );
                        },
                    },
                },
                expectedResult(dependencies): { keywordSearchImpressions: KeywordSearchImpressionsDto[] } {
                    const restaurantId = dependencies.restaurants[0]._id.toString();
                    const keywordSearchImpressions = lambdaResponse.data.monthImpressionsData.map((keywordSearch) => ({
                        id: expect.any(String),
                        restaurantId,
                        year: previousMonth.year,
                        month: previousMonth.month,
                        keywordSearch: keywordSearch.keywordSearch,
                        value: keywordSearch.value,
                        type: keywordSearch.type,
                        relatedKeywordIds: keywordSearch.relatedSelectedKeywords,
                    }));

                    return { keywordSearchImpressions };
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedKeywordSearchImpressions = testCase.getExpectedResult().keywordSearchImpressions;

            const platformId = seededObjects.platforms[0]._id.toString();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const previousMonthYearMonthIndex = previousMonth.year * 100 + previousMonth.month;

            // before use case execution
            // -------------------------------------------------------------------------------------

            // assert that keywordSearchImpressions for previous month do not exist
            const currentKeywordSearchImpressions = await keywordSearchImpressionsRepository.find({
                filter: { restaurantId, yearMonthIndex: previousMonthYearMonthIndex },
                options: { lean: true },
            });
            expect(currentKeywordSearchImpressions).toEqual([]);

            // assert that restaurant has no selected keywords
            const restaurantKeywords = await restaurantKeywordsRepository.find({
                filter: { restaurantId, selected: true },
                options: { lean: true },
            });
            expect(restaurantKeywords).toEqual([]);

            // after use case execution
            // -------------------------------------------------------------------------------------
            await fetchAndSaveKeywordsSearchImpressionsUseCase.execute({ platformId });

            // assert that keywordSearchImpressions for previous month have been created
            const keywordSearchImpressions = await keywordSearchImpressionsRepository.find({
                filter: { restaurantId, yearMonthIndex: previousMonthYearMonthIndex },
                options: { lean: true },
            });
            const keywordSearchImpressionsDTO = keywordSearchImpressions.map((doc) =>
                keywordSearchImpressionsRepository.toEntity(doc).toDTO()
            );
            expect(expectedKeywordSearchImpressions).toIncludeSameMembers(keywordSearchImpressionsDTO);
        });
    });
});
