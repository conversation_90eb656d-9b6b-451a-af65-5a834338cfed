/**
 * If you add a member to this enum you should update `malouErrorCodeToHttpStatus`.
 * You can also add a traduction in `getReadableErrorForMalouErrorCode` for the frontend if needed.
 */
export enum MalouErrorCode {
    // GENERAL ERRORS
    BODY_VALIDATION_ERROR = 'BODY_VALIDATION_ERROR',
    QUERY_VALIDATION_ERROR = 'QUERY_VALIDATION_ERROR',
    PARAMS_VALIDATION_ERROR = 'PARAMS_VALIDATION_ERROR',
    INVALID_URL = 'INVALID_URL',
    TOO_MANY_REQUESTS = 'TOO_MANY_REQUESTS',

    // AGENDA
    AGENDA_DATABASE_CONNECTION_FAILED = 'AGENDA_DATABASE_CONNECTION_FAILED',

    // AI
    CREATE_COMPLETION_ERROR = 'CREATE_COMPLETION_ERROR',
    NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL = 'NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL',
    TEXT_TYPE_NOT_SUPPORTED = 'TEXT_TYPE_NOT_SUPPORTED',
    OPEN_AI_REQUEST_FAILED = 'OPEN_AI_REQUEST_FAILED',
    AI_REQUEST_FAILED = 'AI_REQUEST_FAILED',
    AI_INVALID_RESPONSE_FORMAT = 'AI_INVALID_RESPONSE_FORMAT',

    // API KEYS
    INVALID_API_KEY = 'INVALID_API_KEY',

    // ATTRIBUTES
    ATTRIBUTES_GMB_NOT_CONNECTED = 'ATTRIBUTES_GMB_NOT_CONNECTED',

    // AUTOMATIONS
    INCONSISTENT_RESTAURANT_ID = 'INCONSISTENT_RESTAURANT_ID',

    // CAMPAIGN
    CAMPAIGN_MISSING_QUERY_PARAM = 'CAMPAIGN_MISSING_QUERY_PARAM',
    CAMPAIGN_NOT_FOUND = 'CAMPAIGN_NOT_FOUND',
    CAMPAIGN_DUPLICATE_RECORD_ERROR = 'CAMPAIGN_DUPLICATE_RECORD_ERROR',

    // CASL
    CASL_UNKNOWN_ROLE = 'CASL_UNKNOWN_ROLE',
    CASL_USER_NOT_DEFINED = 'CASL_USER_NOT_DEFINED',

    // CLIENTS
    CLIENT_CANNOT_PARSE_FILE = 'CLIENT_CANNOT_PARSE_FILE',
    CLIENT_LANGUAGE_NOT_FOUND__REQUIRED_HEADERS = 'CLIENT_LANGUAGE_NOT_FOUND__REQUIRED_HEADERS',
    CLIENT_CANNOT_PARSE_DATA = 'CLIENT_CANNOT_PARSE_DATA',

    // COMMENTS
    COMMENT_INVALID_TYPE = 'COMMENT_INVALID_TYPE',
    COMMENT_NOT_FOUND = 'COMMENT_NOT_FOUND',

    // CREDENTIALS
    CREDENTIALS_USECASE_NOT_FOUND = 'CREDENTIAL_USECASE_NOT_FOUND',
    CREDENTIALS_PLATFORM_LOGIN_ERROR = 'CREDENTIALS_PLATFORM_LOGIN_ERROR',
    CREDENTIALS_MISSING_PARAM = 'CREDENTIALS_MISSING_PARAM',
    CREDENTIALS_INSTAGRAM_NOT_FOUND = 'CREDENTIALS_INSTAGRAM_NOT_FOUND',
    CREDENTIALS_FACEBOOK_NOT_FOUND = 'CREDENTIALS_FACEBOOK_NOT_FOUND',
    CREDENTIALS_FACEBOOK_ERROR = 'CREDENTIALS_FACEBOOK_ERROR',
    CREDENTIALS_FACEBOOK_PLATFORM_NOT_SUPPORTED = 'CREDENTIALS_FACEBOOK_PLATFORM_NOT_SUPPORTED',
    CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED = 'CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED',
    CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES = 'CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES',
    CREDENTIALS_TIKTOK_REFRESH_TOKEN_EXPIRED = 'CREDENTIALS_TIKTOK_REFRESH_TOKEN_EXPIRED',
    CREDENTIALS_TIKTOK_NOT_FOUND = 'CREDENTIALS_TIKTOK_NOT_FOUND',
    CREDENTIALS_GMB_REFRESH_TOKEN_EXPIRED = 'CREDENTIALS_GMB_REFRESH_TOKEN_EXPIRED',
    CREDENTIALS_GMB_CREDENTIAL_NOT_FOUND = 'CREDENTIALS_GMB_CREDENTIAL_NOT_FOUND',
    CREDENTIALS_GMB_API_ERROR = 'CREDENTIALS_GMB_API_ERROR',
    CREDENTIALS_GMB_API_DETAILS_ERROR = 'CREDENTIALS_GMB_API_DETAILS_ERROR',
    CREDENTIALS_GMB_POST_SCHEDULE_END_DATE_IN_PAST = 'CREDENTIALS_GMB_POST_SCHEDULE_END_DATE_IN_PAST',
    CREDENTIALS_GMB_API_DESCRIPTION_ERROR = 'CREDENTIALS_GMB_API_DESCRIPTION_ERROR',
    CREDENTIALS_PUPPETEER_OPENTABLE_COOKIE_ERROR = 'CREDENTIALS_PUPPETEER_OPENTABLE_COOKIE_ERROR',
    CREDENTIALS_PUPPETEER_OPENTABLE_OTUMAMIAUTH_COOKIE_NOT_FOUND = 'CREDENTIALS_PUPPETEER_OPENTABLE_OTUMAMIAUTH_COOKIE_NOT_FOUND',
    CREDENTIALS_TRIPADVISOR_COOKIE_NOT_FOUND = 'CREDENTIALS_TRIPADVISOR_COOKIE_NOT_FOUND',
    CREDENTIALS_TRIPADVISOR_UPDATE_COOKIE_FAILED = 'CREDENTIALS_TRIPADVISOR_UPDATE_COOKIE_FAILED',
    CREDENTIALS_UBEREATS_COOKIE_ERROR = 'CREDENTIALS_UBEREATS_COOKIE_ERROR',
    CREDENTIALS_UBEREATS_COOKIE_NOT_FOUND = 'CREDENTIALS_UBEREATS_COOKIE_NOT_FOUND',
    CREDENTIALS_UBEREATS_UPDATE_COOKIE_FAILED = 'CREDENTIALS_UBEREATS_UPDATE_COOKIE_FAILED',
    CREDENTIALS_ZENCHEF_AUTH_ERROR = 'CREDENTIALS_ZENCHEF_AUTH_ERROR',
    CREDENTIALS_ZENCHEF_WRONG_CREDENTIALS = 'CREDENTIALS_ZENCHEF_WRONG_CREDENTIALS',
    CREDENTIALS_ZENCHEF_NO_PARAMETERS_ACCESS = 'CREDENTIALS_ZENCHEF_NO_PARAMETERS_ACCESS',
    CREDENTIALS_ZENCHEF_ERROR = 'CREDENTIALS_ZENCHEF_ERROR',
    CREDENTIALS_ZENCHEF_CREDENTIAL_NOT_FOUND = 'CREDENTIALS_ZENCHEF_CREDENTIAL_NOT_FOUND',
    CREDENTIALS_ZENCHEF_API_ERROR = 'CREDENTIALS_ZENCHEF_API_ERROR',
    CREDENTIALS_DELIVEROO_COULD_NOT_FETCH_API_WITH_SEVERAL_AGENTS = 'CREDENTIALS_DELIVEROO_COULD_NOT_FETCH_API_WITH_SEVERAL_AGENTS',
    DELIVEROO_CREDENTIALS_EXPIRED = 'DELIVEROO_CREDENTIALS_EXPIRED',
    CREDENTIALS_DELIVEROO_NOT_FOUND = 'CREDENTIALS_DELIVEROO_NOT_FOUND',

    // EMAILS
    EMAIL_NOT_SENT = 'EMAIL_NOT_SENT',
    EMAIL_INVALID_CATEGORY = 'EMAIL_INVALID_CATEGORY',
    MAILING_NO_USERS = 'MAILING_NO_USERS',

    // DIAGNOSTICS
    DIAGNOSTIC_NOT_FOUND = 'DIAGNOSTIC_NOT_FOUND',
    DIAGNOSTIC_INSTAGRAM_PAGE_NOT_FOUND = 'DIAGNOSTIC_INSTAGRAM_PAGE_NOT_FOUND',
    ERROR_WHILE_CREATING_DIAGNOSTIC = 'ERROR_WHILE_CREATING_DIAGNOSTIC',
    DIAGNOSTIC_INVALID_CATEGORY = 'DIAGNOSTIC_INVALID_CATEGORY',
    DIAGNOSTIC_CANNOT_FETCH_PLACE_DETAILS = 'DIAGNOSTIC_CANNOT_FETCH_PLACE_DETAILS',
    DIAGNOSTIC_NO_KEYWORDS_FOUND = 'DIAGNOSTIC_NO_KEYWORDS_FOUND',
    DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC = 'DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC',

    // FILTERS
    FILTER_START_DATE_AFTER_END_DATE = 'FILTER_START_DATE_AFTER_END_DATE',
    FILTER_MISSING_PARAMS = 'FILTER_MISSING_PARAMS',
    FILTER_CANNOT_INITIATE_CLASS = 'FILTER_CANNOT_INITIATE_CLASS',
    FILTER_METHOD_NOT_IMPLEMENTED = 'FILTER_METHOD_NOT_IMPLEMENTED',
    FILTER_TIME_RANGE_TOO_SHORT = 'FILTER_TIME_RANGE_TOO_SHORT',
    FILTER_TIME_RANGE_TOO_LONG = 'FILTER_TIME_RANGE_TOO_LONG',
    FILTER_INVALID_INSIGHT_PLATFORM = 'FILTER_INVALID_INSIGHT_PLATFORM',

    // FOLDERS
    FOLDER_WITH_SAME_NAME_ALREADY_EXISTS = 'FOLDER_WITH_SAME_NAME_ALREADY_EXISTS',

    // FACEBOOK
    FACEBOOK_API_EXCEPTION = 'FACEBOOK_API_EXCEPTION',

    // FACEBOOK REEL
    FACEBOOK_REEL_UPLOAD_VIDEO_FAILED = 'FACEBOOK_REEL_UPLOAD_VIDEO_FAILED',
    FACEBOOK_REEL_BAD_UPLOAD_STATUS = 'FACEBOOK_REEL_BAD_UPLOAD_STATUS',
    FACEBOOK_REEL_PUBLISH_REEL_FAILED = 'FACEBOOK_REEL_PUBLISH_REEL_FAILED',
    FACEBOOK_REEL_GET_POST_ID_FAILED = 'FACEBOOK_REEL_GET_POST_ID_FAILED',

    // FEEDBACKS
    FEEDBACK_ALREADY_EXISTS = 'FEEDBACK_ALREADY_EXISTS',

    // GIFT DRAW
    CLIENT_ALREADY_PLAYED = 'CLIENT_ALREADY_PLAYED',
    CLIENT_EMAIL_NOT_FOUND = 'CLIENT_EMAIL_NOT_FOUND',
    CLIENT_NOT_FOUND = 'CLIENT_NOT_FOUND',
    GIFT_DRAW_NOT_FOUND = 'GIFT_DRAW_NOT_FOUND',
    DRAW_GIFT_WENT_WRONG = 'DRAW_GIFT_WENT_WRONG',

    // GMB
    GMB_MESSAGES_AGENT_ALREADY_EXISTS = 'GMB_MESSAGES_AGENT_ALREADY_EXISTS',
    GMB_NOT_CONNECTED = 'GMB_NOT_CONNECTED',
    GMB_PLACE_ID_IS_NO_LONGER_VALID = 'GMB_PLACE_ID_IS_NO_LONGER_VALID',
    GMB_MISSING_PARAMS = 'GMB_MISSING_PARAMS',
    GMB_GEOLOCATION_SERVICE_ERROR = 'GMB_GEOLOCATION_SERVICE_ERROR',
    GMB_MISSING_ADDRESS = 'GMB_MISSING_ADDRESS',
    GMB_FAILED_TO_PUBLISH_POST = 'GMB_FAILED_TO_PUBLISH_POST',
    GMB_PIN_DROP_REQUIRED = 'GMB_PIN_DROP_REQUIRED',
    GMB_STOREFRONT_REQUIRED_FOR_CATEGORY = 'GMB_STOREFRONT_REQUIRED_FOR_CATEGORY',
    GMB_PLACE_ACTION_LINK_ALREADY_EXISTS_WITH_SAME_DOMAIN = 'GMB_PLACE_ACTION_LINK_ALREADY_EXISTS_WITH_SAME_DOMAIN',
    GMB_ACCOUNT_NOT_FOUND = 'GMB_ACCOUNT_NOT_FOUND',

    // HELPERS
    HELPERS_RETRY_PLATFORM_SCRAPPER_ERROR = 'HELPERS_RETRY_PLATFORM_SCRAPPER_ERROR',
    HELPERS_INJECTOR_NOT_IMPLEMENTED = 'HELPERS_INJECTOR_NOT_IMPLEMENTED',
    HELPERS_DOWNLOAD_FILE_MISSING_FOLDER = 'HELPERS_DOWNLOAD_FILE_MISSING_FOLDER',

    // INSIGHTS
    INSIGHTS_NOT_FOUND = 'INSIGHTS_NOT_FOUND',
    DOWNLOAD_INSIGHTS_AS_PDF_FAILED = 'DOWNLOAD_INSIGHTS_AS_PDF_FAILED',
    INSIGHTS_MAPPER_UNSUPPORTED_METRIC = 'INSIGHTS_MAPPER_UNSUPPORTED_METRIC',

    // JOBS
    NO_MIN_REDIRECTED_AT = 'NO_MIN_REDIRECTED_AT',

    // KEYWORD LAMBDA
    KEYWORD_LAMBDA_GENERATOR_ERROR = 'KEYWORD_LAMBDA_ERROR',

    // KEYWORDS
    KEYWORD_NOT_FOUND = 'KEYWORD_NOT_FOUND',
    KEYWORDS_GEOSAMPLE_ERROR = 'KEYWORDS_GEOSAMPLE_ERROR',
    KEYWORDS_PLATFORM_NOT_AUTHORIZED = 'KEYWORDS_PLATFORM_NOT_AUTHORIZED',
    KEYWORDS_VOLUME_API_REQUEST_LIMIT_REACHED = 'KEYWORDS_VOLUME_API_REQUEST_LIMIT_REACHED',
    FETCH_KEYWORDS_VOLUME_MONTHLY_ERROR = 'FETCH_KEYWORDS_VOLUME_MONTHLY_ERROR',
    FETCH_KEYWORDS_VOLUME_MONTHLY_SUCCESS = 'FETCH_KEYWORDS_VOLUME_MONTHLY_SUCCESS', // used for Slack alerts
    KEYWORDS_VOLUME_API_REQUEST_FAILED = 'KEYWORDS_VOLUME_API_REQUEST_FAILED',
    KEYWORDS_GENERATION_ALREADY_RUNNING = 'KEYWORDS_GENERATION_ALREADY_RUNNING',

    // MEDIAS
    WRONG_MEDIA_FORMAT = 'WRONG_MEDIA_FORMAT',
    WRONG_MEDIA_TYPE = 'WRONG_MEDIA_TYPE',
    CANNOT_DELETE_MEDIAS_FROM_OTHER_RESTAURANTS = 'CANNOT_DELETE_MEDIAS_FROM_OTHER_RESTAURANTS',
    MEDIA_THUMBNAIL_GENERATION_FAILED = 'MEDIA_THUMBNAIL_GENERATION_FAILED',

    // MEDIAS v2
    CANNOT_COMPUTE_TRANSFORM_DATA_FROM_OLD_MEDIA = 'CANNOT_COMPUTE_TRANSFORM_DATA_FROM_OLD_MEDIA',
    CANNOT_GET_THUMBNAIL_FOR_EDITION_MEDIA = 'CANNOT_GET_THUMBNAIL_FOR_EDITION_MEDIA',

    // MESSAGES
    MISSING_AWS_SOURCE_KEY = 'MISSING_AWS_SOURCE_KEY',
    MESSAGES_PLATFORM_HAS_NO_CONVERSATION_MAPPER = 'MESSAGES_PLATFORM_HAS_NO_CONVERSATION_MAPPER',
    MESSAGES_CLIENT_AUTH_FAILED = 'MESSAGES_CLIENT_AUTH_FAILED',

    // MISC
    CREATION_ERROR = 'CREATION_ERROR',
    NO_FILTER_PROVIDED = 'NO_FILTER_PROVIDED',
    NOT_FOUND = 'NOT_FOUND',
    BAD_REQUEST = 'BAD_REQUEST',
    UNAUTHORIZED = 'UNAUTHORIZED',
    FORBIDDEN = 'FORBIDDEN',
    INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
    ABSTRACT_CLASS_ERROR = 'ABSTRACT_CLASS_ERROR',
    MISSING_PARAM = 'MISSING_PARAM',
    INVALID_DATA = 'INVALID_DATA',

    // MULTIMEDIA_STREAMS_INFORMATION
    MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION = 'MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION',

    // NFCS
    NFC_NOT_FOUND = 'NFC_NOT_FOUND',

    // NOT_IMPLEMENTED
    NOT_IMPLEMENTED = 'NOT_IMPLEMENTED',

    // ORGANIZATIONS
    ORGANIZATION_NOT_FOUND = 'ORGANIZATION_NOT_FOUND',
    ORGANIZATION_IS_LINKED_TO_A_RESTAURANT = 'ORGANIZATION_IS_LINKED_TO_A_RESTAURANT',
    ORGANIZATION_LIMIT_REACHED = 'ORGANIZATION_LIMIT_REACHED',

    // PLATFORMS
    PLATFORM_NOT_FOUND = 'PLATFORM_NOT_FOUND',
    RETRIEVE_OVERVIEW_DATA_ERROR = 'RETRIEVE_OVERVIEW_DATA_ERROR',
    PLATFORM_CREDENTIAL_NOT_FOUND = 'PLATFORM_CREDENTIAL_NOT_FOUND',
    PLATFORM_DATA_UNDEFINED_OR_EMPTY = 'PLATFORM_DATA_UNDEFINED_OR_EMPTY',
    PLATFORM_DATA_CRAWLING_ERROR = 'PLATFORM_DATA_CRAWLING_ERROR',
    PLATFORM_SEARCH_RESULT_NOT_ARRAY = 'PLATFORM_SEARCH_RESULT_NOT_ARRAY',
    PLATFORM_CATEGORY_NOT_FOUND = 'PLATFORM_CATEGORY_NOT_FOUND',
    PLATFORM_FB_NOT_CONNECTED = 'PLATFORM_FB_NOT_CONNECTED',
    PLATFORM_IG_NOT_CONNECTED = 'PLATFORM_IG_NOT_CONNECTED',
    PLATFORM_NO_DATA_IN_RESPONSE = 'PLATFORM_NO_DATA_IN_RESPONSE',
    PLATFORM_MAPPER_METHOD_NOT_IMPLEMENTED = 'PLATFORM_MAPPER_METHOD_NOT_IMPLEMENTED',
    PLATFORM_FOURSQUARE_NO_VENUES = 'PLATFORM_FOURSQUARE_NO_VENUES',
    PLATFORM_UBEREATS_REVIEWS_FETCH_ERROR = 'PLATFORM_UBEREATS_REVIEWS_FETCH_ERROR',
    PLATFORM_ZENCHEF_FETCH_RESTAURANTS_UNAUTHORIZED_ERROR = 'PLATFORM_ZENCHEF_FETCH_RESTAURANTS_UNAUTHORIZED_ERROR',
    PLATFORM_SCRAPPER_ERROR = 'PLATFORM_SCRAPPER_ERROR',
    PLATFORM_NOT_CONNECTED = 'PLATFORM_NOT_CONNECTED',
    PLATFORM_MAPPER_FAILED_FIELD_PUBLISH = 'PLATFORM_MAPPER_FAILED_FIELD_PUBLISH',
    PLATFORM_MAPPER_CANNOT_PARSE_DATA = 'PLATFORM_MAPPER_CANNOT_PARSE_DATA',
    PLATFORM_MAPPER_MISSING_PARAM = 'PLATFORM_MAPPER_MISSING_PARAM',
    PLATFORM_MAPPER_DATA_ERROR = 'PLATFORM_MAPPER_DATA_ERROR',
    PLATFORM_SERVICE_ERROR = 'PLATFORM_SERVICE_ERROR',
    PLATFORM_SERVICE_NOT_IMPLEMENTED = 'PLATFORM_SERVICE_NOT_IMPLEMENTED',
    PLATFORM_API_ENDPOINT_NOT_FOUND = 'PLATFORM_API_ENDPOINT_NOT_FOUND',
    PLATFORM_INVALID_ENDPOINT = 'PLATFORM_INVALID_ENDPOINT',
    PLATFORM_INVALID_KEY = 'PLATFORM_INVALID_KEY',
    PLATFORM_THE_FORK_REVIEWS_MALFORMED_RESPONSE = 'PLATFORM_THE_FORK_REVIEWS_MALFORMED_RESPONSE',
    PLATFORM_REQUIRED = 'PLATFORM_REQUIRED',
    PLATFORM_MISSING_SOCIAL_ID = 'PLATFORM_MISSING_SOCIAL_ID',
    PLATFORM_MISSING_LOCATION_ID = 'PLATFORM_MISSING_LOCATION_ID',
    PLATFORM_MISSING_PERMISSIONS = 'PLATFORM_MISSING_PERMISSIONS',
    PLATFORM_SCRAP_ERROR = 'PLATFORM_SCRAP_ERROR',
    PLATFORM_DELETE_ERROR = 'PLATFORM_DELETE_ERROR',
    PLATFORM_PULL_OVERVIEW_ERROR = 'PLATFORM_PULL_OVERVIEW_ERROR',
    PLATFORM_UPSERT_ERROR = 'PLATFORM_UPSERT_ERROR',
    PLATFORM_PUBLISH_ERROR = 'PLATFORM_PUBLISH_ERROR',
    PLATFORM_INVALID_PASSWORD = 'PLATFORM_INVALID_PASSWORD',
    PLATFORM_NO_PROFILE_PICTURE_URL = 'PLATFORM_NO_PROFILE_PICTURE_URL',
    FETCH_YELP_WEBSITE_FAILED = 'FETCH_YELP_WEBSITE_FAILED',
    PLATFORM_DELIVEROO_DRN_ID_NOT_FOUND = 'PLATFORM_DELIVEROO_DRN_ID_NOT_FOUND',

    // PLATFORM INSIGHTS
    PLATFORM_INSIGHTS_ERROR = 'PLATFORM_INSIGHTS_ERROR',

    // POSTS
    COMPLETE_PUBLISH_POST_ERROR = 'COMPLETE_PUBLISH_POST_ERROR',
    COMPLETE_PUBLISH_POST_TIMEOUT_ERROR = 'COMPLETE_PUBLISH_POST_TIMEOUT_ERROR',
    MAPSTR_MAPPING_ERROR = 'MAPSTR_MAPPING_ERROR',
    MEDIA_NOT_READY_MAX_RETRIES_REACHED = 'MEDIA_NOT_READY_MAX_RETRIES_REACHED',
    MEDIA_DELETED_FOR_POST = 'MEDIA_DELETED_FOR_POST',
    POST_NOT_FOUND = 'POST_NOT_FOUND',
    UNKNOWN_FB_POST_TYPE = 'UNKNOWN_FB_POST_TYPE',
    FAILED_VIDEO_RESIZE = 'FAILED_VIDEO_RESIZE',
    MEDIA_REQUIRED_BECAUSE_THE_STORY_IS_PLANNED_FOR_PUBLICATION = 'MEDIA_REQUIRED_BECAUSE_THE_STORY_IS_PLANNED_FOR_PUBLICATION',
    STORY_ALREADY_PUBLISHED = 'STORY_ALREADY_PUBLISHED',
    PUBLISH_STORY_ERROR = 'PUBLISH_STORY_ERROR',
    POSTS_MUST_BE_FROM_SAME_RESTAURANT = 'POSTS_MUST_BE_FROM_SAME_RESTAURANT',
    POST_CREATION_FOR_PLATFORM_FAILED = 'POST_CREATION_FOR_PLATFORM_FAILED',
    PUBLISH_POST_ERROR = 'PUBLISH_POST_ERROR',
    POST_MUST_HAVE_MEDIA = 'POST_MUST_HAVE_MEDIA',
    COMPLETE_PUBLISH_STORY_ERROR = 'COMPLETE_PUBLISH_STORY_ERROR',
    REEL_WITHOUT_VIDEO = 'REEL_WITHOUT_VIDEO',
    POST_IS_NOT_SOCIAL_POST = 'POST_IS_NOT_SOCIAL_POST',
    POST_IS_PUBLISHING = 'POST_IS_PUBLISHING',

    // POST PUBLICATION V2
    MISSING_DATA_ON_MEDIA_FOR_TRANSFORM = 'MISSING_DATA_ON_MEDIA_FOR_TRANSFORM',
    // PROVIDERS
    PROVIDER_MAPSTR_NO_DATA = 'PROVIDER_MAPSTR_NO_DATA',
    PROVIDER_INVALID_PLATFORM_KEY = 'PROVIDER_INVALID_PLATFORM_KEY',

    // SERVICES
    SERVICE_INVALID_CONFIGURATION = 'SERVICE_INVALID_CONFIGURATION',
    SERVICE_INVALID_RESPONSE = 'SERVICE_INVALID_RESPONSE',

    // PUBSUB
    NO_SUBSCRIPTION_NAME_OR_ID = 'NO_SUBSCRIPTION_NAME_OR_ID',

    // REPORTS
    INVALID_UNSUBSCRIBE_HASH = 'INVALID_UNSUBSCRIBE_HASH',

    // RESTAURANTS
    RESTAURANT_HAS_NO_ORGANIZATION = 'RESTAURANT_HAS_NO_ORGANIZATION',
    RESTAURANT_MISSING_LATLNG = 'RESTAURANT_MISSING_LATLNG',
    RESTAURANT_API_LOCATION_ID_NOT_FOUND = 'RESTAURANT_API_LOCATION_ID_NOT_FOUND',
    RESTAURANT_NOT_FOUND = 'RESTAURANT_NOT_FOUND',
    RESTAURANT_BRICKS_NOT_FOUND = 'RESTAURANT_BRICKS_NOT_FOUND',
    ABOVE_RESTAURANTS_LIMITS = 'ABOVE_RESTAURANTS_LIMITS',
    RESTAURANT_INACTIVE = 'RESTAURANT_INACTIVE',
    INVALID_SOCIAL_NETWORK_URL = 'INVALID_SOCIAL_NETWORK_URL',
    RESTAURANT_CLOSED_PERMANENTLY = 'RESTAURANT_CLOSED_PERMANENTLY',

    // RESTAURANT AI SETTINGS
    RESTAURANT_AI_SETTINGS_NOT_FOUND = 'RESTAURANT_AI_SETTINGS_NOT_FOUND',

    // RESTAURANT KEYWORDS
    RESTAURANT_KEYWORD_NOT_FOUND = 'RESTAURANT_KEYWORD_NOT_FOUND',
    RESTAURANT_KEYWORD_RANKING_REFRESH_NOT_ALLOWED = 'RESTAURANT_KEYWORD_RANKING_REFRESH_NOT_ALLOWED',

    // REVIEWS
    PUBLISH_REVIEW_ERROR = 'PUBLISH_REVIEW_ERROR',
    RETRIEVE_REVIEWS_ERROR = 'RETRIEVE_REVIEWS_ERROR',
    REVIEWS_UPDATE_ERROR = 'REVIEWS_UPDATE_ERROR',
    REVIEW_NOT_FOUND = 'REVIEW_NOT_FOUND',
    REVIEW_TOO_OLD = 'REVIEW_TOO_OLD',
    REVIEW_NOT_IN_RESULTS = 'REVIEW_NOT_IN_RESULTS',
    REVIEW_INCORRECT_SOCIAL_LINK = 'REVIEW_INCORRECT_SOCIAL_LINK',
    REVIEW_INCORRECT_SOCIAL_ID = 'REVIEW_INCORRECT_SOCIAL_ID',
    DOWNLOAD_REVIEWS_AS_PDF_FAILED = 'DOWNLOAD_REVIEWS_AS_PDF_FAILED',
    FAILED_SEMANTIC_ANALYSIS = 'FAILED_SEMANTIC_ANALYSIS',

    // ROUTING_ERROR
    ROUTING_ERROR = 'ROUTING_ERROR',

    // SEMANTIC ANALYSIS
    SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_FOUND = 'SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_FOUND',
    SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_USER_INPUT = 'SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_USER_INPUT',

    // SCRAPPER
    SCRAPPER_TOO_MANY_TRIES = 'SCRAPPER_TOO_MANY_TRIES',

    // STORE LOCATOR
    STORE_LOCATOR_REVIEWS_COUNT_INSUFFICIENT = 'STORE_LOCATOR_REVIEWS_COUNT_INSUFFICIENT',
    STORE_LOCATOR_ADDRESS_NOT_FOUND = 'STORE_LOCATOR_ADDRESS_NOT_FOUND',
    STORE_LOCATOR_DATA_FETCH_FAILED = 'STORE_LOCATOR_DATA_FETCH_FAILED',
    STORE_LOCATOR_DATA_BACKUP_USED = 'STORE_LOCATOR_DATA_BACKUP_USED',

    // STICKERS
    STICKER_NOT_FOUND = 'STICKER_NOT_FOUND',

    // TEMPLATES
    DEFAULT_TEMPLATES_GENERATION_FAILED = 'DEFAULT_TEMPLATES_GENERATION_FAILED',

    // TIKTOK
    TIKTOK_OAUTH_SCOPE_NOT_ACCEPTED = 'TIKTOK_OAUTH_SCOPE_NOT_ACCEPTED',

    // UBEREATS
    UBEREATS_STORE_NOT_FOUND = 'UBEREATS_STORE_NOT_FOUND',

    // USERS
    USER_HAS_NO_ORGANIZATION = 'USER_HAS_NO_ORGANIZATION',
    USER_UNKNOWN_ROLE = 'USER_UNKNOWN_ROLE',
    USER_CANNOT_UPDATE_USER_RESTAURANT = 'USER_CANNOT_UPDATE_USER_RESTAURANT',
    USER_CANNOT_CREATE_ADMIN = 'USER_CANNOT_CREATE_ADMIN',
    USER_NOT_FOUND = 'USER_NOT_FOUND',
    USER_NOT_VERIFIED = 'USER_NOT_VERIFIED',
    USER_WRONG_PASSWORD = 'USER_WRONG_PASSWORD',
    USER_CANNOT_DELETE_ADMIN = 'USER_CANNOT_DELETE_ADMIN',
    USER_CANNOT_DELETE_USER = 'USER_CANNOT_DELETE_USER',
    USER_HAVE_NO_ACCESS_TO_ACCOUNT = 'USER_HAVE_NO_ACCESS_TO_ACCOUNT',
    USER_CANNOT_SEE_RESTAURANTS = 'USER_CANNOT_SEE_RESTAURANTS',
    USER_CANNOT_DOWNGRADE_LAST_OWNER = 'USER_CANNOT_DOWNGRADE_LAST_OWNER',
    USER_HAS_LINKED_ENTITIES = 'USER_HAS_LINKED_ENTITIES',

    // WHEEL OF FORTUNE
    GIFT_NAME_TOO_LONG = 'GIFT_NAME_TOO_LONG',
    NO_AVAILABLE_GIFTS = 'NO_AVAILABLE_GIFTS',
    RESTAURANT_HAS_AN_ACTIVE_OR_PROGRAMMED_WHEEL_OF_FORTUNE = 'RESTAURANT_HAS_AN_ACTIVE_OR_PROGRAMMED_WHEEL_OF_FORTUNE',
    RESTAURANT_NOT_MANAGED_MISSING_IN_WHEEL_OF_FORTUNE = 'RESTAURANT_NOT_MANAGED_MISSING_IN_WHEEL_OF_FORTUNE',
    RESTAURANT_WHEEL_OF_FORTUNE_MULTIPLE_RESTAURANTS = 'RESTAURANT_WHEEL_OF_FORTUNE_MULTIPLE_RESTAURANTS',
    WHEEL_OF_FORTUNE_NOT_FOUND = 'WHEEL_OF_FORTUNE_NOT_FOUND',
    WHEEL_OF_FORTUNE_NOT_STARTED = 'WHEEL_OF_FORTUNE_NOT_STARTED',
    WRONG_RETRIEVAL_DATES = 'WRONG_RETRIEVAL_DATES',

    // LAMBDA
    LAMBDA_ERROR_GENERATING_PDF = 'LAMBDA_ERROR_GENERATING_PDF',
    LAMBDA_MAXIMUM_CALL_STACK_SIZE_EXCEEDED = 'LAMBDA_MAXIMUM_CALL_STACK_SIZE_EXCEEDED',
    LAMBDA_FUNCTION_NAME_NOT_FOUND = 'LAMBDA_FUNCTION_NAME_NOT_FOUND',

    // YEXT
    YEXT_ACCOUNT_NOT_FOUND = 'YEXT_ACCOUNT_NOT_FOUND',
    YEXT_LOCATION_NOT_FOUND = 'YEXT_LOCATION_NOT_FOUND',
    YEXT_LOCATION_ALREADY_EXISTS = 'YEXT_LOCATION_ALREADY_EXISTS',
    YEXT_LOCATION_STATUS_IS_NOT_PROCESSED = 'YEXT_LOCATION_STATUS_IS_NOT_PROCESSED',
    YEXT_LOCATION_HAS_ACTIVE_SERVICES = 'YEXT_LOCATION_HAS_ACTIVE_SERVICES',
    YEXT_CANT_DELETE_BECAUSE_OF_ADD_REQUEST_STATUS = 'YEXT_CANT_DELETE_BECAUSE_OF_ADD_REQUEST_STATUS',
    YEXT_NOT_SUPPORTED = 'YEXT_NOT_SUPPORTED',
    CANNOT_MAP_RESTAURANT_TO_YEXT_ENTITY = 'CANNOT_MAP_RESTAURANT_TO_YEXT_ENTITY',
    CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT = 'CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT',
    UPDATE_YEXT_ADD_REQUEST_STATUS_MAX_RETRY = 'UPDATE_YEXT_ADD_REQUEST_STATUS_MAX_RETRY',
    YEXT_UPDATE_LOCATION_FAILED = 'YEXT_UPDATE_LOCATION_FAILED',
    YEXT_DELETE_LOCATION_FAILED = 'YEXT_DELETE_LOCATION_FAILED',
    YEXT_LOCATION_DELETION_FAILED = 'YEXT_LOCATION_DELETION_FAILED',
    UNHANDLED_YEXT_PUBLISHERS = 'UNHANDLED_YEXT_PUBLISHERS',

    // SQS
    SQS_MESSAGE_NOT_FOUND = 'SQS_MESSAGE_NOT_FOUND',
    SQS_INVALID_MESSAGE = 'SQS_INVALID_MESSAGE',

    // ROI
    ROI_ACTIVATION_MESSAGE = 'ROI_ACTIVATION_MESSAGE',
    MONTHLY_SAVE_ROI_INSIGHTS_ERROR = 'MONTHLY_SAVE_ROI_INSIGHTS_ERROR',

    // HUBSPOT
    HUBSPOT_CANNOT_SUBMIT_FORM = 'HUBSPOT_CANNOT_SUBMIT_FORM',

    // NOTIFICATIONS
    REVIEWS_FROM_DIFFERENT_RESTAURANTS = 'REVIEWS_FROM_DIFFERENT_RESTAURANTS',
    NOTIFICATION_NOT_FOUND = 'NOTIFICATION_NOT_FOUND',

    FAILED_TO_PROCESS_KEYWORD_SEARCH_IMPRESSIONS = 'FAILED_TO_PROCESS_KEYWORD_SEARCH_IMPRESSIONS',

    // DATES
    INVALID_DATE_RANGE = 'INVALID_DATE_RANGE',
    INVALID_COMPARISON_PERIOD = 'INVALID_COMPARISON_PERIOD',
}
